import {BindingScope, injectable, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {ConversationRepository, TaskRepository} from '../repositories';

interface ProductUrlData {
  name: string;
  productUrl?: string;
  imageUrl?: string;
  id?: string;
  price?: number;
  source: string;
  timestamp: string;
  salesData?: {
    quantitySold?: number;
    totalRevenue?: number;
    rank?: number;
  };
}

interface ProductUrlStorage {
  products: ProductUrlData[];
}

@injectable({scope: BindingScope.TRANSIENT})
export class ProductUrlCaptureService {
  constructor(
    @repository(ConversationRepository)
    private conversationRepository: ConversationRepository,
    @repository(TaskRepository)
    private taskRepository: TaskRepository,
  ) {}

  /**
   * Capture product URLs from tool results and store them in conversation
   */
  async captureProductUrlsInConversation(
    conversationId: number,
    productUrls: ProductUrlData[]
  ): Promise<void> {
    try {
      if (!productUrls || productUrls.length === 0) {
        return;
      }

      console.log(`ProductUrlCaptureService: Capturing ${productUrls.length} product URLs for conversation ${conversationId}`);

      // Get existing conversation
      const conversation = await this.conversationRepository.findById(conversationId);
      if (!conversation) {
        console.warn(`ProductUrlCaptureService: Conversation ${conversationId} not found`);
        return;
      }

      // Get existing product URLs or initialize empty structure
      let existingProductUrls: ProductUrlStorage = { products: [] };
      if (conversation.productUrls) {
        existingProductUrls = conversation.productUrls as ProductUrlStorage;
      }

      // Filter out duplicates based on product ID or URL
      const newProductUrls = productUrls.filter(newUrl => {
        const isDuplicate = existingProductUrls.products.some(existing => {
          if (newUrl.id && existing.id) {
            return existing.id === newUrl.id;
          }
          if (newUrl.productUrl && existing.productUrl) {
            return existing.productUrl === newUrl.productUrl;
          }
          return existing.name.toLowerCase() === newUrl.name.toLowerCase();
        });
        return !isDuplicate;
      });

      if (newProductUrls.length === 0) {
        console.log('ProductUrlCaptureService: No new product URLs to capture (all duplicates)');
        return;
      }

      // Add new URLs to existing ones (most recent first)
      existingProductUrls.products.unshift(...newProductUrls);

      // Limit to last 50 product URLs to prevent bloat
      const MAX_PRODUCT_URLS = 50;
      if (existingProductUrls.products.length > MAX_PRODUCT_URLS) {
        existingProductUrls.products = existingProductUrls.products.slice(0, MAX_PRODUCT_URLS);
      }

      // Update conversation with captured product URLs
      await this.conversationRepository.updateById(conversationId, {
        productUrls: existingProductUrls,
        updatedAt: new Date()
      });

      console.log(`ProductUrlCaptureService: Successfully captured ${newProductUrls.length} product URLs in conversation ${conversationId}`);
    } catch (error) {
      console.error('ProductUrlCaptureService: Error capturing product URLs in conversation:', error);
    }
  }

  /**
   * Capture product URLs from conversation and store them in associated task
   */
  async captureProductUrlsInTask(
    taskId: number,
    conversationId?: number
  ): Promise<void> {
    try {
      console.log(`ProductUrlCaptureService: Capturing product URLs for task ${taskId}`);

      // Get the task
      const task = await this.taskRepository.findById(taskId);
      if (!task) {
        console.warn(`ProductUrlCaptureService: Task ${taskId} not found`);
        return;
      }

      // Get conversation ID from task or parameter
      const convId = conversationId || task.conversationId;
      if (!convId) {
        console.log('ProductUrlCaptureService: No conversation ID available for task');
        return;
      }

      // Get conversation with product URLs
      const conversation = await this.conversationRepository.findById(convId);
      if (!conversation || !conversation.productUrls) {
        console.log('ProductUrlCaptureService: No product URLs found in conversation');
        return;
      }

      const conversationProductUrls = conversation.productUrls as ProductUrlStorage;
      if (!conversationProductUrls.products || conversationProductUrls.products.length === 0) {
        console.log('ProductUrlCaptureService: No product URLs to transfer to task');
        return;
      }

      // Get existing task product URLs or initialize empty structure
      let existingTaskProductUrls: ProductUrlStorage = { products: [] };
      if (task.productUrls) {
        existingTaskProductUrls = task.productUrls as ProductUrlStorage;
      }

      // Merge conversation product URLs with existing task product URLs
      const allProductUrls = [...conversationProductUrls.products, ...existingTaskProductUrls.products];
      
      // Remove duplicates
      const uniqueProductUrls = allProductUrls.filter((url, index, self) => {
        return index === self.findIndex(u => {
          if (url.id && u.id) {
            return u.id === url.id;
          }
          if (url.productUrl && u.productUrl) {
            return u.productUrl === url.productUrl;
          }
          return u.name.toLowerCase() === url.name.toLowerCase();
        });
      });

      // Update task with captured product URLs
      await this.taskRepository.updateById(taskId, {
        productUrls: { products: uniqueProductUrls }
      });

      console.log(`ProductUrlCaptureService: Successfully captured ${uniqueProductUrls.length} product URLs in task ${taskId}`);
    } catch (error) {
      console.error('ProductUrlCaptureService: Error capturing product URLs in task:', error);
    }
  }

  /**
   * Get product URLs from conversation
   */
  async getProductUrlsFromConversation(conversationId: number): Promise<ProductUrlData[]> {
    try {
      const conversation = await this.conversationRepository.findById(conversationId);
      if (!conversation || !conversation.productUrls) {
        return [];
      }

      const productUrls = conversation.productUrls as ProductUrlStorage;
      return productUrls.products || [];
    } catch (error) {
      console.error('ProductUrlCaptureService: Error getting product URLs from conversation:', error);
      return [];
    }
  }

  /**
   * Get product URLs from task
   */
  async getProductUrlsFromTask(taskId: number): Promise<ProductUrlData[]> {
    try {
      const task = await this.taskRepository.findById(taskId);
      if (!task || !task.productUrls) {
        return [];
      }

      const productUrls = task.productUrls as ProductUrlStorage;
      return productUrls.products || [];
    } catch (error) {
      console.error('ProductUrlCaptureService: Error getting product URLs from task:', error);
      return [];
    }
  }
}
