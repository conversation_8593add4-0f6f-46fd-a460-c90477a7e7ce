import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {ConversationRepository} from '../repositories';

@injectable({scope: BindingScope.TRANSIENT})
export class ProductUrlService {
  constructor(
    @repository(ConversationRepository)
    private conversationRepository: ConversationRepository,
  ) {}

  /**
   * Get product URLs for a conversation, with fallback to find by task or campaign
   */
  async getProductUrls(task?: any, campaign?: any): Promise<string> {
    try {
      let conversationId: number | null = null;

      // First priority: Use campaign.chatId (most reliable link)
      if (campaign?.chatId) {
        conversationId = campaign.chatId;
      }
      // Second priority: Use task.conversationId
      else if (task?.conversationId) {
        conversationId = task.conversationId;
      }
      // Third priority: Find conversation by taskId
      else if (task?.id) {
        try {
          const conversation = await this.conversationRepository.findOne({
            where: { taskId: task.id }
          });
          if (conversation) {
            conversationId = conversation.id!;
          }
        } catch (error) {
          console.log('Could not find conversation by taskId:', error);
        }
      }

      if (!conversationId) {
        return '';
      }

      // Get conversation with product URLs
      const conversation = await this.conversationRepository.findById(conversationId);

      if (!conversation.productUrls || conversation.productUrls.length === 0) {
        return '';
      }

      // Format product URLs for the prompt
      const formattedUrls = conversation.productUrls.slice(0, 10).map((product) => {
        let productInfo = `- ${product.name}`;
        if (product.productUrl) {
          productInfo += `\n  Product URL: ${product.productUrl}`;
        }
        if (product.imageUrl) {
          productInfo += `\n  Image URL: ${product.imageUrl}`;
        }
        if (product.price) {
          productInfo += `\n  Price: $${product.price}`;
        }
        if (product.salesData) {
          productInfo += `\n  Sales: ${product.salesData.quantitySold} sold, $${product.salesData.totalRevenue} revenue`;
        }
        return productInfo;
      }).join('\n\n');

      return `Product URLs and information from conversation:\n${formattedUrls}`;
    } catch (error) {
      console.error('Error getting product URLs:', error);
      return '';
    }
  }
}
