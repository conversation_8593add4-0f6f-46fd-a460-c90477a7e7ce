import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {ConversationProductUrl, ConversationProductUrlRelations, Conversation} from '../models';
import {ConversationRepository} from './conversation.repository';

export class ConversationProductUrlRepository extends DefaultCrudRepository<
  ConversationProductUrl,
  typeof ConversationProductUrl.prototype.id,
  ConversationProductUrlRelations
> {
  public readonly conversation: BelongsToAccessor<Conversation, typeof ConversationProductUrl.prototype.id>;

  constructor(
    @inject('datasources.devDb') dataSource: DevDbDataSource,
    @repository.getter('ConversationRepository') protected conversationRepositoryGetter: Getter<ConversationRepository>,
  ) {
    super(ConversationProductUrl, dataSource);
    this.conversation = this.createBelongsToAccessorFor('conversation', conversationRepositoryGetter,);
    this.registerInclusionResolver('conversation', this.conversation.inclusionResolver);
  }
}
