import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Conversation} from './conversation.model';

@model()
export class ConversationProductUrl extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: false,
  })
  productUrl?: string;

  @property({
    type: 'string',
    required: false,
  })
  imageUrl?: string;

  @property({
    type: 'string',
    required: false,
  })
  productId?: string;

  @property({
    type: 'number',
    required: false,
  })
  price?: number;

  @property({
    type: 'object',
    required: false,
  })
  salesData?: {
    quantitySold?: number;
    totalRevenue?: number;
    rank?: number;
  };

  @property({
    type: 'date',
    defaultFn: 'now',
  })
  createdAt?: string;

  @belongsTo(() => Conversation)
  conversationId: number;

  constructor(data?: Partial<ConversationProductUrl>) {
    super(data);
  }
}

export interface ConversationProductUrlRelations {
  conversation?: Conversation;
}

export type ConversationProductUrlWithRelations = ConversationProductUrl & ConversationProductUrlRelations;
