import {Entity, model, property, hasMany} from '@loopback/repository';
import {TaskStep} from './task-step.model';

@model()
export class Task extends Entity {
  @property({
    type: 'string',
  })
  klaviyoTemplateId?: string;

  @property({
    type: 'string',
  })
  klaviyoCampaignId?: string;

  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'date',
    required: true,
  })
  scheduledDate: string;

  @property({
    type: 'string',
    required: true,
  })
  description: string;

  @property({
    type: 'string',
  })
  taskType?: string;

  @property({
    type: 'string',
    default: 'Ready',
  })
  status?: string;

  @property({
    type: 'number',
  })
  plannerCampaignId?: number;

  @property({
	type: 'boolean'
  })
  isTemplate?: boolean;

  @property({
    type: 'object',
    jsonSchema: {
      type: 'object',
      title: 'Email Design',
      description: 'The Unlayer email design JSON structure'
    }
  })
  emailDesign?: object;

  @property({
    type: 'string',
    jsonSchema: {
      title: 'Email HTML',
      description: 'The rendered HTML content for the email'
    }
  })
  emailHtml?: string;

  @property({
    type: 'string',
  })
  version?: string;

  @property({
    type: 'number',
    jsonSchema: {
      title: 'Conversation ID',
      description: 'The ID of the conversation associated with this task'
    }
  })
  conversationId?: number;

  @property({
    type: 'object',
    jsonSchema: {
      title: 'Product URLs',
      description: 'Captured product URLs and image URLs from chat conversations',
      type: 'object',
      properties: {
        products: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              productUrl: { type: 'string' },
              imageUrl: { type: 'string' },
              id: { type: 'string' },
              price: { type: 'number' },
              source: { type: 'string' },
              timestamp: { type: 'string' }
            }
          }
        }
      }
    }
  })
  productUrls?: object;

  @hasMany(() => TaskStep)
  taskSteps: TaskStep[];

  constructor(data?: Partial<Task>) {
    super(data);
  }
}

export interface TaskRelations {
  // describe navigational properties here
}

export type TaskWithRelations = Task & TaskRelations;
