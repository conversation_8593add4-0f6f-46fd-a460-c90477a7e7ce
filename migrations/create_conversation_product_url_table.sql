-- Migration to create conversation_product_url table for storing product URLs
-- This replaces storing product URLs in the llmMetadata field

CREATE TABLE IF NOT EXISTS conversation_product_url (
    id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    product_url TEXT,
    image_url TEXT,
    product_id VARCHAR(255),
    price DECIMAL(10,2),
    sales_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT fk_conversation_product_url_conversation 
        FOREIGN KEY (conversation_id) 
        REFERENCES conversation(id) 
        ON DELETE CASCADE,
    
    -- Index for better query performance
    INDEX idx_conversation_product_url_conversation_id (conversation_id),
    INDEX idx_conversation_product_url_created_at (created_at)
);

-- Add comments for documentation
COMMENT ON TABLE conversation_product_url IS 'Stores product URLs captured from chat tool results, replacing storage in llmMetadata field';
COMMENT ON COLUMN conversation_product_url.conversation_id IS 'References the conversation where the product URLs were captured';
COMMENT ON COLUMN conversation_product_url.name IS 'Product name or title';
COMMENT ON COLUMN conversation_product_url.product_url IS 'Full URL to the product page using organization external domain';
COMMENT ON COLUMN conversation_product_url.image_url IS 'URL to the product image';
COMMENT ON COLUMN conversation_product_url.product_id IS 'Product ID from the source system (e.g., Shopify)';
COMMENT ON COLUMN conversation_product_url.price IS 'Product price if available';
COMMENT ON COLUMN conversation_product_url.sales_data IS 'JSON object containing sales metrics like quantity sold, revenue, rank';
COMMENT ON COLUMN conversation_product_url.created_at IS 'Timestamp when the product URL was captured';
