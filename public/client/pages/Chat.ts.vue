<template>
  <div class="relative flex flex-col h-full bg-[#1d1644]">
    <!-- Integration Status Banner - Full Width at Top -->
    <IntegrationStatusBanner />

    <div class="flex flex-1 overflow-hidden relative rounded-tl-xl bg-[#F5F5F5]">
      <div
        class="flex flex-col flex-1 items-center px-2 md:px-3"
        :class="{
          'w-full': !isArtifactDrawerOpen,
          'w-[calc(100%-var(--artifact-drawer-width))]': isArtifactDrawerOpen && !isMobile,
          'hidden': isArtifactDrawerOpen && isMobile,
        }"
        :style="isArtifactDrawerOpen && !isMobile ? { '--artifact-drawer-width': artifactDrawerWidth + 'px' } : {}"
      >

        <div
          ref="chatHistoryContainer"
          class="p-4 space-y-4 overflow-y-auto flex-1 w-full chat-scrollbar relative"
          :class="[
            isMobile ? 'max-w-full mx-2' : (!isArtifactDrawerOpen ? 'md:max-w-[50vw] mx-6' : 'max-w-full mx-6')
          ]"
          @scroll="handleScroll"
        >
          <!-- Scroll to Bottom Button -->

          <!-- Loading State -->
          <div
            v-if="isLoadingConversation"
            class="flex flex-col items-center justify-center h-full pt-10"
          >
            <div class="relative">
              <div
                class="animate-spin rounded-full h-16 w-16 border-4 border-gray-200"
              ></div>
              <div
                class="animate-spin rounded-full h-16 w-16 border-t-4 border-[#6E41FF] absolute top-0 left-0"
              ></div>
            </div>
            <p class="text-gray-600 text-lg font-medium mt-6">
              Loading conversation...
            </p>
            <p class="text-gray-500 text-sm mt-2">
              Retrieving your chat history
            </p>
          </div>

          <!-- Initial Prompts -->
          <div
            v-else-if="chatSegments.length === 0"
            class="flex flex-col items-center justify-center h-full pt-10"
          >
            <InitialPrompts
              :prompts="initialPrompts"
              @select-prompt="handlePromptSelected"
            />
          </div>

          <div
            v-for="segment in chatSegments"
            :key="segment.id"
            :class="[
              'flex',
              segment.sender === 'user' ? 'justify-end' : 'justify-start',
            ]"
          >
            <div
              v-if="segment.type === 'text' && segment.sender === 'user'"
              :class="[
                'bg-purple-50 border border-purple-100 rounded-2xl p-4 text-gray-600 shadow-sm',
                isMobile ? 'max-w-[90vw]' : (!isArtifactDrawerOpen ? 'md:max-w-[50vw]' : 'md:max-w-full')
              ]"
            >
              <div
                v-if="
                  segment.content &&
                  TextFormattingService.containsFormatting(segment.content)
                "
                v-html="TextFormattingService.formatText(segment.content)"
              ></div>
              <template v-else>{{ segment.content }}</template>
            </div>

            <div
              v-if="
                segment.type === 'text' &&
                segment.sender === 'ai' &&
                ((segment.content && segment.content.trim() !== '') ||
                 isWaitingForAI)
              "
              :class="[
                'rounded-lg px-4 py-2 whitespace-pre-wrap bg-[#F5F5F5] text-[#202020]',
                isMobile ? 'max-w-[90vw]' : (!isArtifactDrawerOpen ? 'md:max-w-[50vw]' : 'md:max-w-full')
              ]"
            >
              <div v-if="segment.isHtml" v-html="segment?.content?.trim()"></div>
              <div
                v-else-if="
                  segment.content &&
                  TextFormattingService.containsFormatting(
                    segment.content.trim(),
                  )
                "
                v-html="
                  TextFormattingService.formatText(segment.content.trim())
                "
              ></div>
              <template v-else>{{ segment?.content?.trim() }}</template>
            </div>

            <!-- Generated/Edited Image Display -->
            <SingleImageRenderer
              v-else-if="
                (segment.type === 'image' ||
                  segment.type === 'generatedImage') &&
                segment.imageUrl
              "
              :imageId="segment.id"
              :imageUrl="segment.imageUrl"
              :imageName="segment.content || ''"
              :altText="
                segment.type === 'generatedImage'
                  ? 'AI Generated Image'
                  : 'Image'
              "
              :initialLoadState="segment.imageLoaded || false"
              :class="[
                isMobile ? 'max-w-[90vw]' : (!isArtifactDrawerOpen ? 'md:max-w-[50vw]' : 'md:max-w-full')
              ]"
              @image-loaded="handleImageLoad"
              @image-error="handleImageError"
            />

            <!-- Multi-Image Display -->
            <MultiImageRenderer
              v-else-if="segment.type === 'multiimage' && segment.images"
              :imageId="segment.id"
              :images="segment.images"
              :title="'Multiple Images'"
              :debug="false"
              :class="[
                isMobile ? 'max-w-[90vw]' : (!isArtifactDrawerOpen ? 'md:max-w-[50vw]' : 'md:max-w-full')
              ]"
              @multi-image-loaded="handleMultiImageLoad"
              @multi-image-error="handleMultiImageError"
              @refresh-all-images="handleRefreshAllImages"
            />

            <div
              v-else-if="segment.type === 'memory' && segment.memory"
              class="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-4 shadow-sm max-w-md"
            >
              <div class="flex items-center gap-2 mb-2">
                <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <div class="text-sm font-semibold text-purple-800">
                  Capturing brand memory
                </div>
              </div>
              <div class="text-xs text-purple-600 font-medium mb-1">
                {{ segment.memory.category }}
              </div>
              <div class="text-sm text-gray-700 whitespace-pre-wrap">
                {{ segment.memory.info }}
              </div>
            </div>

            <div
              v-else-if="segment.type === 'switch_mode' && segment.switchModeData"
              class="bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-200 rounded-xl p-4 shadow-sm max-w-md cursor-pointer hover:shadow-md transition-shadow"
              @click="handleSwitchModeClick(segment.switchModeData)"
            >
              <div class="flex items-center gap-2 mb-2">
                <svg
                  class="w-4 h-4 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"
                  />
                </svg>
                <div class="text-sm font-semibold text-blue-800">
                  Switch Mode
                </div>
              </div>
              <div class="text-xs text-blue-600 font-medium mb-1 uppercase">
                {{ segment.switchModeData.mode }}
              </div>
              <div class="text-sm text-gray-700 whitespace-pre-wrap mb-2">
                {{ segment.switchModeData.message }}
              </div>
              <div class="text-xs text-blue-500 font-medium">
                Click to switch →
              </div>
            </div>

            <BriefChatDisplay
              v-if="
                [
                  'brief_placeholder',
                  'brief_artifact',
                  'email_placeholder',
                  'email_artifact',
                  'plan_placeholder',
                  'plan_artifact',
                ].includes(segment.type)
              "
              :segment="segment as any"
              :artifact-type="getArtifactType(segment.type)"
              @click="handleArtifactClick(segment)"
              class="cursor-pointer"
            >
              <!-- Add a small error indicator if there's a parse error -->
              <template
                v-if="segment.hasParseError && segment.type === 'plan_artifact'"
                #icon-overlay
              >
                <div
                  class="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                >
                  !
                </div>
              </template>
            </BriefChatDisplay>

			<ToolMessage
              v-if="segment.type === 'tool_message'"
              :message="segment.content || ''"
              :is-active="segment.isGenerating || false"
            />

            <!-- Build Plan Trigger Placeholder -->
            <BuildPlanChatDisplay
              v-if="segment.type === 'buildplan_trigger'"
              :segment="segment as any"
            />
          </div>

          <div
            v-if="isWaitingForAI && !isActivelyStreaming"
            class="flex justify-start items-center space-x-2"
          >
            <div
              class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
              style="animation-delay: -0.3s"
            ></div>
            <div
              class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
              style="animation-delay: -0.15s"
            ></div>
            <div
              class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
            ></div>
            <span class="text-sm text-gray-500">Thinking...</span>
          </div>
        </div>

        <!-- Scroll to Bottom Button moved to just above the input area -->

        <div
          class="p-4 border-t border-gray-200 shadow-md bg-white w-full relative"
          :class="[
            isMobile ? 'max-w-full mx-0' : (!isArtifactDrawerOpen ? 'md:max-w-[50vw] mx-0 md:mx-6' : 'max-w-full mx-0 md:mx-6'),
            {
              'rounded-xl': chatSegments.length > 0,
              'mb-auto rounded-xl': chatSegments.length === 0,
            }
          ]"
        >
          <!-- Down Arrow Button -->
          <button
            v-if="!isAtBottom && chatSegments.length > 0"
            @click="scrollToBottom(true)"
            class="absolute left-1/2 transform -translate-x-1/2 -top-8 p-1.5 rounded-full bg-purple-100 hover:bg-purple-200 transition-colors duration-200 text-purple-700 focus:outline-none z-10 shadow-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <div>
            <!-- Image Preview Area -->
            <div
              v-if="attachedImages.length > 0"
              class="flex flex-wrap gap-2 mb-2"
            >
              <div
                v-for="(image, index) in attachedImages"
                :key="index"
                class="relative"
              >
                <img
                  :src="image.dataUrl"
                  class="w-24 h-24 object-cover rounded-lg"
                />
                <button
                  @click="removeImage(index)"
                  class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center"
                >
                  ×
                </button>
              </div>
            </div>
          </div>

          <!-- Quick Action Chat Prompts -->
          <QuickChatPrompts
            :quick-actions="selectedQuickActions"
            :is-disabled="isAnyContentGenerating"
            @quick-action-selected="handlePromptSelected"
          />

          <div
            class="flex items-end bg-white rounded-lg p-3 border border-gray-200 mb-3 w-full min-h-fit transition-[height] duration-200"
          >
            <textarea
              v-model="newMessage"
              ref="chatInput"
              :placeholder="isAnyContentGenerating ? 'Continue typing your next message while AI generates...' : 'Message your AI campaign assistant...'"
              class="flex-1 bg-transparent border-none focus:ring-0 focus:outline-none px-1 text-gray-600 placeholder-gray-400 w-full resize-none"
              @input="autoResize"
              @keydown.enter.exact="handleEnterKey"
              @keydown.shift.enter="handleShiftEnterKey"
              :class="{'opacity-70': isAnyContentGenerating}"
              @click="($event?.target as HTMLTextAreaElement)?.focus()"
            ></textarea>
            <button
              v-if="selectedAction === 'flows'"
              class="ml-2 p-1.5 rounded-md bg-gray-100 text-gray-400 cursor-not-allowed"
              disabled
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                height="20px"
                viewBox="0 -960 960 960"
                width="20px"
                fill="currentColor"
              >
                <path
                  d="M460-80q-92 0-156-64t-64-156v-420q0-66 47-113t113-47q66 0 113 47t47 113v380q0 42-29 71t-71 29q-42 0-71-29t-29-71v-380h60v380q0 17 11.5 28.5T460-300q17 0 28.5-11.5T500-340v-380q0-42-29-71t-71-29q-42 0-71 29t-29 71v420q0 66 47 113t113 47q66 0 113-47t47-113v-420h60v420q0 92-64 156T460-80Z"
                />
              </svg>
            </button>
            <input
              ref="fileInput"
              type="file"
              accept="image/png,image/jpeg,image/jpg,image/webp"
              multiple
              class="hidden"
              @change="handleImageUpload"
              data-max-size="10000000"
            />
            <button
              @click="sendMessage"
              :disabled="
                (!newMessage.trim() && attachedImages.length === 0) ||
                isAnyContentGenerating
              "
              class="ml-2 p-1.5 rounded-md bg-[#E9D5FF] text-[#5A16C9] transition-all duration-200 relative"
              :class="[
                (newMessage.trim() || attachedImages.length > 0) &&
                !isAnyContentGenerating
                  ? 'hover:bg-[#D8B4FE]'
                  : 'bg-purple-100 text-purple-300 cursor-not-allowed'
              ]"
              :title="isAnyContentGenerating ? 'AI is generating content. You can keep typing but can\'t send until it completes.' : ''"
            >
              <span v-if="isAnyContentGenerating" class="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-green-400 animate-pulse" title="AI is generating content. You can still type but cannot send yet."></span>
              <span v-if="isAnyContentGenerating" class="sr-only">AI is generating content. You can continue typing.</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                height="20px"
                viewBox="0 -960 960 960"
                width="20px"
                fill="currentColor"
              >
                <path
                  d="M440-160v-487L216-423l-56-57 320-320 320 320-56 57-224-224v487h-80Z"
                />
              </svg>
            </button>
          </div>

          <!-- Action Buttons -->
          <ChatQuickOptions
            v-model="selectedAction"
            :is-disabled="isAnyContentGenerating"
            :chat-has-started="chatSegments.length > 0"
          />
        </div>

        <div
          class="text-center text-xs text-gray-500 py-2 w-full"
          :class="[
            isMobile ? 'max-w-full' : (!isArtifactDrawerOpen ? 'md:max-w-[50vw]' : 'max-w-full')
          ]"
        >
          Raleon AI can make mistakes, always double check before sending.
        </div>
      </div>

      <ChatArtifact
        v-if="isArtifactDrawerOpen"
        :key="`artifact-drawer-${artifactInitialView}`"
        @close-artifact-drawer="toggleArtifactDrawer"
        @width-change="handleArtifactWidthChange"
        @retry-parse="handleRetryParse"
        @campaign-built="handleCampaignBuiltFromArtifact"
        :brief-markdown-content="finalBrief"
        :streaming-brief-content="streamingBriefContent"
        :generated-email-design="generatedEmailDesign"
        :is-generating-email="isGeneratingEmail"
        :generated-plan-data="generatedPlanData"
        :is-generating-plan="isGeneratingPlan"
        :streaming-plan-content="streamingPlanContent"
        :has-parse-error="currentPlanHasParseError"
        :raw-plan-content="currentPlanRawContent"
        :initial-view="artifactInitialView"
        :chat-id="conversationId ? parseInt(conversationId) : undefined"
        :style="!isMobile ? { width: artifactDrawerWidth + 'px' } : {}"
        :class="[
          isMobile ? 'fixed inset-0 w-full h-full bg-white z-50' : 'relative bg-white shadow-lg border-l border-gray-200 z-10'
        ]"
      ></ChatArtifact>
    </div>
    <MessageQuotaModal :show="showQuotaModal" @close="showQuotaModal=false" />
    <OnboardingModal
      v-if="showOnboardingModal"
      @complete="handleOnboardingComplete"
      @skip="handleOnboardingSkip"
    />
    <MobileChatWarningModal
      :show="showMobileWarningModal"
      @close="showMobileWarningModal = false"
    />
  </div>
</template>

<script lang="ts">
import {jsonrepair} from 'jsonrepair';
import {
  defineComponent,
  nextTick,
  ref,
  computed,
  watch,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
} from '@vue/runtime-core';
// Icons are now imported in QuickActionOptions.ts
import TopChatBar from '../components/TopChatBar.ts.vue';
import ChatArtifact from '../components/ChatArtifact.ts.vue';
import BriefChatDisplay from '../components/BriefChatDisplay.ts.vue';
import InitialPrompts from '../components/InitialPrompts.ts.vue';
import BuildPlanChatDisplay from '../components/BuildPlanChatDisplay.ts.vue';
import MultiImageRenderer from '../components/agent-task/MultiImageRenderer.vue';
import SingleImageRenderer from '../components/agent-task/SingleImageRenderer.vue';
import ChatQuickOptions, {
  QuickActionOption,
} from '../components/ChatQuickOptions.ts.vue';
import QuickChatPrompts from '../components/QuickChatPrompts.ts.vue';
import MessageQuotaModal from '../components/MessageQuotaModal.ts.vue';
import IntegrationStatusBanner from '../components/IntegrationStatusBanner.ts.vue';
import OnboardingModal from '../components/OnboardingModal.vue';
import MobileChatWarningModal from '../components/MobileChatWarningModal.vue';
import ToolMessage from '../components/ToolMessage.vue';
import {
  getQuickActionsForType,
  getInitialPromptsForType,
} from '../constants/QuickActionOptions';
import {customerIOTrackEvent} from '../services/customerio.js';
import * as Utils from '../../client-old/utils/Utils';
import {startConversation, postMessage} from '../services/chatService';
// MessageParserService and related types are no longer directly used here
// import {
//   MessageParserService,
//   ParsedSegment,
//   TextSegment,
//   ToolStartSegment,
//   ToolContentSegment,
//   ToolEndSegment
// } from '../services/messageParserService';
import type {MessageContent} from '../services/chatService';
import {TextFormattingService} from '../services/textFormattingService';
import {initializeToolHandlers, toolEvents} from '../services/tools';
import {fetchMessageQuota, useQuotaService} from '../services/messageQuotaService';
// ImageProcessingService is used by the composable now
// import { ImageProcessingService } from '../services/imageProcessService';
import {messageArtifactService} from '../services/messageArtifactService';
import {ToolMessageService} from '../services/toolMessageService';
import {
  useChatStreaming
} from '../composables/useChatStreaming';

import { ChatMessageSegment } from '../services/messageArtifactService';

export default defineComponent({
  name: 'Chat',
  components: {
    TopChatBar,
    ChatArtifact,
    BriefChatDisplay,
    InitialPrompts,
    BuildPlanChatDisplay,
    MultiImageRenderer,
    SingleImageRenderer,
    ChatQuickOptions,
    QuickChatPrompts,
    MessageQuotaModal,
    IntegrationStatusBanner,
    OnboardingModal,
    MobileChatWarningModal,
    ToolMessage,
  },
  setup() {
    const fileInput = ref<HTMLInputElement | null>(null);
    const chatInput = ref<HTMLTextAreaElement | null>(null);
    const chatSegments = ref<ChatMessageSegment[]>([]);
    const conversationId = ref<string | null>(null);
    const isAtBottom = ref(true);
    const chatHistoryContainer = ref<HTMLElement | null>(null);
    const chatObserver = ref<MutationObserver | null>(null);
    const showQuotaModal = ref(false);
    const showMobileWarningModal = ref(false);

    // Use the reactive quota service
    const quotaService = useQuotaService();

    // Store a reference to the component instance methods
    const componentInstance = ref<any>(null);

    // Update the component instance after mount
    onMounted(async () => {
      componentInstance.value = getCurrentInstance()?.proxy;
      await quotaService.initializeQuota();
      if (typeof window !== 'undefined') {
        window.addEventListener('resize', handleResize);
        handleResize(); // Initial check
        if (window.innerWidth < 768 && !localStorage.getItem('chat_mobile_warning_shown')) {
          showMobileWarningModal.value = true;
        }
      }
    });

    onBeforeUnmount(() => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
      }
    });

    const screenWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 0);
    const handleResize = () => {
      screenWidth.value = window.innerWidth;
    };
    const isMobile = computed(() => screenWidth.value < 768); // md breakpoint

    const finalBriefForArtifact = ref<string | null>(null);
    const generatedEmailDesignForArtifact = ref<any | null>(null);
    const generatedPlanDataForArtifact = ref<any | null>(null);
    const isArtifactDrawerOpen = ref(false);
    const artifactInitialView = ref<'brief' | 'email' | 'plan'>('brief');
    const selectedAction = ref<
      'planMonth' | 'planCampaigns' | 'brainstorm' | 'flows'
    >('planMonth');

    // Track if the sidebar was collapsed due to the artifact drawer
    const collapsedByArtifactDrawer = ref(false);

    const scrollToBottomInternal = () => {
      const instance = getCurrentInstance();
      if (instance && instance.proxy) {
        // Auto-scroll during streaming - scrollToBottom will check if user is at bottom
        (instance.proxy as any).scrollToBottom(false);
      }
    };

    const generateImageApi = async (params: any, placeholderId: string) => {
      const instance = getCurrentInstance();
      if (instance && instance.proxy) {
        await (instance.proxy as any).generateImage(params, placeholderId);
      }
    };
    const editImageApi = async (params: any, placeholderId: string) => {
      const instance = getCurrentInstance();
      if (instance && instance.proxy) {
        await (instance.proxy as any).editImage(params, placeholderId);
      }
    };
    const handleImageUploadFromTokenApi = async (
      base64DataUrl: string,
      style?: string,
    ): Promise<string | undefined> => {
      const instance = getCurrentInstance();
      if (instance && instance.proxy) {
        return (instance.proxy as any).handleImageUploadFromToken(
          base64DataUrl,
          style,
        );
      }
      return undefined;
    };
    const generateEmailFromComponentsApi = async function(componentJSONText: string, placeholderId: string | null) {
      // Call the method on the component instance
      if (componentInstance.value) {
        return componentInstance.value.generateEmailFromComponents(componentJSONText, placeholderId);
      } else {
        console.error('Component instance not available when trying to generate email');
        throw new Error('Component instance not available');
      }
    };
    const setGeneratedPlanDataCallback = (data: any) => {
      generatedPlanDataForArtifact.value = data;
    };

    const isBuildingPlan = ref(false);

    const chatStreaming = useChatStreaming({
      conversationId,
      chatSegments,
      campaignId: ref(''),
      currentBriefText: ref(''),
      onScrollToBottom: scrollToBottomInternal,
      generateImageApi,
      editImageApi,
      handleImageUploadFromTokenApi,
      generateEmailFromComponentsApi,
      setGeneratedPlanData: setGeneratedPlanDataCallback,
    });

    watch(chatStreaming.finalBrief, newVal => {
      // When finalBrief is updated, update finalBriefForArtifact and clear streamingBriefContent
      finalBriefForArtifact.value = newVal;
      if (newVal) {
        // If we have a final brief, clear streaming brief to ensure proper display
        chatStreaming.streamingBriefContent.value = '';
      }
    });

    watch(
      chatSegments,
      () => {
        const last = chatSegments.value[chatSegments.value.length - 1];
        if (
          last &&
          last.type === 'buildplan_trigger' &&
          last.isGenerating === false
        ) {
          if (componentInstance.value) {
            componentInstance.value.handleBuildPlanTrigger();
          }
        }
      },
      {deep: true},
    );

    toolEvents.on('artifact:open', (payload: any) => {
      console.log('Received artifact:open event:', payload);
      if (payload && payload.view) {
        artifactInitialView.value = payload.view;
        if (payload.autoOpen) {
          isArtifactDrawerOpen.value = true;
        }
      }
    });

    // Collapse sidebar on small screens when the drawer opens
    watch(isArtifactDrawerOpen, val => {
      //if (screenWidth.value < 768) {
        if (val) {
          collapsedByArtifactDrawer.value = true;
          toolEvents.emit('sidebar:collapse');
        } else if (collapsedByArtifactDrawer.value) {
          toolEvents.emit('sidebar:expand');
          collapsedByArtifactDrawer.value = false;
        }
      //}
    });

    watch(chatSegments, segments => {
      const last = segments[segments.length - 1];
      if (
        last &&
        last.type === 'buildplan_trigger' &&
        !last.isGenerating &&
        !isBuildingPlan.value
      ) {
        last.isGenerating = true;
        componentInstance.value.handleBuildPlanTrigger();
      }
    });

    const handleScroll = () => {
      if (chatHistoryContainer.value) {
        const {scrollTop, scrollHeight, clientHeight} =
          chatHistoryContainer.value;
        // Consider "at bottom" if within 20px of the bottom
        const wasAtBottom = isAtBottom.value;
        isAtBottom.value = scrollHeight - scrollTop - clientHeight < 20;

        // If we just detected that we're no longer at the bottom during streaming,
        // force a re-render to show the down arrow
        if (wasAtBottom && !isAtBottom.value) {
          console.log('Content went off screen, showing down arrow');
          // Force Vue to update the DOM
          nextTick();
        }
      }
    };

    return {
      fileInput,
      chatSegments,
      conversationId,
      TextFormattingService,
      chatHistoryContainer,
      chatInput,
      isAtBottom,
      handleScroll,
      chatObserver,
      selectedAction, // Expose selectedAction to the template

      isWaitingForAI: chatStreaming.isWaitingForAI,
      isGeneratingEmail: chatStreaming.isGeneratingEmail,
      isGeneratingPlan: chatStreaming.isGeneratingPlan,
      isGeneratingImage: chatStreaming.isGeneratingImage,
      isEditingImage: chatStreaming.isEditingImage,
      streamingBriefContent: chatStreaming.streamingBriefContent,
      finalBrief: finalBriefForArtifact,
      streamingPlanContent: chatStreaming.streamingPlanContent,
      currentPlanHasParseError: chatStreaming.currentPlanHasParseError,
      currentPlanRawContent: chatStreaming.currentPlanRawContent,
      processStream: chatStreaming.processStream,

      newMessage: ref(''),
      isArtifactDrawerOpen,
      artifactDrawerWidth: ref(800),
      isLoadingConversation: ref(false),
      generatedEmailDesign: generatedEmailDesignForArtifact,
      generatedPlanData: generatedPlanDataForArtifact,
      isBuildingPlan,
      artifactInitialView,
      // Get initial prompts from external constants file
      initialPrompts: computed(() =>
        getInitialPromptsForType(selectedAction.value),
      ),
      attachedImages: ref<Array<{file: File; dataUrl: string}>>([]),
      showQuotaModal,
      canSendMessage: quotaService.canSendMessage,
      quota: quotaService.quota,
      showOnboardingModal: ref(false),
      showMobileWarningModal: ref(false),
      isMobile,
      quotaService,
    };
  },
  computed: {
    isAnyContentGenerating(): boolean {
      return (
        this.isWaitingForAI ||
        this.isGeneratingEmail ||
        this.isGeneratingPlan ||
        this.isGeneratingImage ||
        this.isEditingImage
      );
    },
    isActivelyStreaming(): boolean {
      // Return true if there are chat segments from AI that indicate streaming is happening
      const lastSegment = this.chatSegments[this.chatSegments.length - 1];

      // Check if the segment exists, is from AI, and has content or tool calls active
      return !!(lastSegment &&
                lastSegment.sender === 'ai' &&
                (lastSegment.content ||
                // Include tool calls and results in the streaming detection
                (this.isWaitingForAI && (lastSegment.type === 'text' || lastSegment.isGenerating))));
    },
    selectedQuickActions() {
      // Get quick actions from external constants file
      return getQuickActionsForType(this.selectedAction);
    },
  },
  methods: {
    autoResize(event: Event) {
      const el = event.target as HTMLTextAreaElement;
      el.style.height = 'auto';
      const maxHeight = 120;
      const newHeight = el.scrollHeight;
      if (newHeight > maxHeight) {
        el.style.height = maxHeight + 'px';
        el.style.overflowY = 'auto';
      } else {
        el.style.height = newHeight + 'px';
        el.style.overflowY = 'hidden';
      }
    },

    async handleBuildPlanTrigger() {
      if (!this.generatedPlanData) {
        console.error('No generated plan data available to build.');
        this.chatSegments.push({
          id: this.generateUniqueId(),
          type: 'text',
          sender: 'ai',
          content:
            'Error: No plan data is available to build. Please generate a plan first before using the Build Plan action.',
          timestamp: new Date(),
        });
        return;
      }
      if (this.isBuildingPlan) {
        console.log('Already building a plan, ignoring duplicate request');
        return;
      }
      customerIOTrackEvent('Build Plan Triggered');
      this.isBuildingPlan = true;
      try {
        const payload = {
          planName: this.generatedPlanData.name || 'Generated Plan',
          planDescription: this.generatedPlanData.description || '',
          planBusinessGoal: this.generatedPlanData.goal || '',
          planStartDate: this.generatedPlanData.startDate,
          planEndDate: this.generatedPlanData.endDate,
          chatId: this.conversationId, // Add chatId to link campaigns back to conversation
          versionPrompt: this.generatedPlanData.versionPrompt || '',
          versionDescription:
            this.generatedPlanData.versionDescription ||
            'Initial version generated from chat',
          campaigns: (this.generatedPlanData.campaigns || []).map(
            (camp: any) => ({
              name: camp.name || 'Generated Campaign',
              type: camp.campaignType || 'Unknown',
              description: camp.description || '',
              targetSegment: camp.targetSegment || 'General Audience',
              scheduledDate: camp.scheduledDate,
              businessGoal: camp.businessGoal || '',
              promotionTitle: camp.promotionTitle || '',
              promotionDescription: camp.promotionDescription || '',
              promotionType: camp.promotionType || '',
              whyText: camp.whyText || '',
              subjectLine: camp.subjectLine || '',
              previewText: camp.previewText || '',
              emailBriefText: camp.emailBriefText || '',
              emailJSON: camp.emailJSON || '',
              promotionSuggestionReason: camp.promotionSuggestionReason || '',
              taskType: camp.taskType || camp.campaignType,
            }),
          ),
        };
        if (!payload.planName)
          throw new Error('Plan name is missing in generated data.');
        if (!payload.planStartDate || !payload.planEndDate)
          console.warn('Plan start date or end date might be missing.');
        if (!payload.campaigns.every((c: any) => c.scheduledDate))
          throw new Error(
            'One or more campaigns are missing a scheduled date.',
          );

        const api = this.$axios
          ? this.$axios
          : (await import('axios')).default.create({
              baseURL: Utils.URL_DOMAIN,
              headers: {
                Authorization: `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json',
              },
            });
        const response = await api.post('/chat/plan/build', payload);
        if (response.data.success && response.data.planId) {
          const planId = response.data.planId;
          if (this.$router)
            this.$router.push(`/ai-strategist/planning/plan/${planId}`);
          else window.location.href = `/ai-strategist/planning/plan/${planId}`;
        } else {
          const msg = response.data.message || 'Unknown error from server.';
          this.chatSegments.push({
            id: this.generateUniqueId(),
            type: 'text',
            sender: 'ai',
            content: `Error building plan: ${msg}`,
            timestamp: new Date(),
          });
        }
      } catch (error: any) {
        const errorMessage =
          error.response?.data?.message ||
          error.message ||
          'An unexpected error occurred.';
        this.chatSegments.push({
          id: this.generateUniqueId(),
          type: 'text',
          sender: 'ai',
          content: `Error building plan: ${errorMessage}`,
          timestamp: new Date(),
        });
        console.error(
          'Error calling build plan API:',
          error.response?.data || error.message,
        );
      } finally {
        this.isBuildingPlan = false;
      }
    },

    async loadConversation(conversationIdToLoad: string) {
      console.log('Loading conversation:', conversationIdToLoad);
      this.isLoadingConversation = true;
      this.isWaitingForAI = true;
      this.chatSegments = [];
      console.time('loadConversation');
      try {
        const {getConversation} = await import('../services/chatService');
        const conversation = await getConversation(conversationIdToLoad);
        if (!conversation) throw new Error('Failed to load conversation');
        this.conversationId = conversationIdToLoad;
        console.log('Loaded conversation:', conversation);

        if (conversation.promptTemplateId) {
          switch (conversation.promptTemplateId) {
            case 10:
              this.selectedAction = 'planCampaigns';
              break;
            case 11:
              this.selectedAction = 'planMonth';
              break;
            case 13:
              this.selectedAction = 'flows';
              break;
            case 14:
              this.selectedAction = 'brainstorm';
              break;
          }
        }

        // Process all messages in chronological order
        if (conversation.messages && conversation.messages.length > 0) {
          const sortedMessages = [...conversation.messages].sort(
            (a, b) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
          );

          // Process all messages in sequence rather than separating by role
          for (const message of sortedMessages) {
            if (message.role === 'user') {
              if (typeof message.content === 'string') {
                try {
                  const parsedContent = JSON.parse(message.content);
                  if (Array.isArray(parsedContent)) {
                    for (const item of parsedContent) {
                      if (item.type === 'text' && item.text) {
                        this.chatSegments.push({
                          id: this.generateUniqueId(),
                          type: 'text',
                          sender: 'user',
                          content: item.text,
                          timestamp: new Date(message.createdAt),
                        });
                      } else if (
                        item.type === 'image_url' &&
                        item.image_url?.url
                      ) {
                        this.chatSegments.push({
                          id: this.generateUniqueId(),
                          type: 'image',
                          sender: 'user',
                          imageUrl: item.image_url.url,
                          imageLoaded: false,
                          timestamp: new Date(message.createdAt),
                        });
                      }
                    }
                  } else {
                    this.chatSegments.push({
                      id: this.generateUniqueId(),
                      type: 'text',
                      sender: 'user',
                      content: message.content,
                      timestamp: new Date(message.createdAt),
                    });
                  }
                } catch (e) {
                  this.chatSegments.push({
                    id: this.generateUniqueId(),
                    type: 'text',
                    sender: 'user',
                    content: message.content,
                    timestamp: new Date(message.createdAt),
                  });
                }
              } else if (Array.isArray(message.content)) {
                for (const item of message.content) {
                  if (item.type === 'text' && item.text) {
                    this.chatSegments.push({
                      id: this.generateUniqueId(),
                      type: 'text',
                      sender: 'user',
                      content: item.text,
                      timestamp: new Date(message.createdAt),
                    });
                  } else if (item.type === 'image_url' && item.image_url?.url) {
                    this.chatSegments.push({
                      id: this.generateUniqueId(),
                      type: 'image',
                      sender: 'user',
                      imageUrl: item.image_url.url,
                      imageLoaded: false,
                      timestamp: new Date(message.createdAt),
                    });
                  }
                }
              }
            } else if (message.role === 'assistant') {
              // Process assistant message immediately after its corresponding user message
              this.processAssistantMessage(message);
            } else if (message.role === 'tool') {
              // Process tool message to show what function was called
              this.processToolMessage(message);
            }
          }
        }
      } catch (error: any) {
        console.error('Error loading conversation:', error);
        this.chatSegments.push({
          id: this.generateUniqueId(),
          type: 'text',
          sender: 'ai',
          content: 'Error: Failed to load conversation. Please try again.',
          timestamp: new Date(),
        });
      } finally {
        this.isWaitingForAI = false;
        this.isLoadingConversation = false;
        // Force scroll to bottom with a slight delay to ensure DOM has updated
        setTimeout(() => {
          this.scrollToBottom(true);
        }, 100);
        console.timeEnd('loadConversation');
        console.log(
          'Conversation loaded with segments:',
          this.chatSegments.length,
        );
      }
    },

    handleImageLoad(imageId: string) {
      const segmentIndex = this.chatSegments.findIndex(s => s.id === imageId);
      if (segmentIndex !== -1) {
        this.chatSegments[segmentIndex].imageLoaded = true;
        this.chatSegments[segmentIndex].imageError = false;
      }
    },

    handleImageError(event: Event, imageId: string) {
      const segmentIndex = this.chatSegments.findIndex(s => s.id === imageId);
      if (segmentIndex !== -1) {
        this.chatSegments[segmentIndex].imageError = true;
        this.chatSegments[segmentIndex].imageLoaded = false;
      }
    },

    handleMultiImageLoad(
      segmentId: string,
      imageIndex: number,
      updatedImages?: any[],
    ) {
      const segmentIndex = this.chatSegments.findIndex(
        segment => segment.id === segmentId,
      );
      if (
        segmentIndex !== -1 &&
        this.chatSegments[segmentIndex].images &&
        this.chatSegments[segmentIndex].images![imageIndex]
      ) {
        const currentImage =
          this.chatSegments[segmentIndex].images![imageIndex];
        if (currentImage.loaded === true && !currentImage.error) return;
        const newImages = [...this.chatSegments[segmentIndex].images!];
        if (updatedImages) {
          if (JSON.stringify(newImages) !== JSON.stringify(updatedImages)) {
            this.chatSegments[segmentIndex].images = updatedImages;
          } else {
            return;
          }
        } else {
          newImages[imageIndex] = {
            ...newImages[imageIndex],
            loaded: true,
            error: false,
          };
          this.chatSegments[segmentIndex].images = newImages;
        }
      }
    },

    handleMultiImageError(
      segmentId: string,
      imageIndex: number,
      updatedImages?: any[],
    ) {
      const segmentIndex = this.chatSegments.findIndex(
        segment => segment.id === segmentId,
      );
      if (
        segmentIndex !== -1 &&
        this.chatSegments[segmentIndex].images &&
        this.chatSegments[segmentIndex].images![imageIndex]
      ) {
        if (updatedImages) {
          this.chatSegments[segmentIndex].images = updatedImages;
        } else {
          this.chatSegments[segmentIndex].images![imageIndex].loaded = true;
          this.chatSegments[segmentIndex].images![imageIndex].error = true;
        }
        const updatedSegment = {...this.chatSegments[segmentIndex]};
        this.$nextTick(() => {
          this.chatSegments.splice(segmentIndex, 1, updatedSegment);
        });
      }
    },

    handleRefreshAllImages(segmentId: string, updatedImages: any[]) {
      const segmentIndex = this.chatSegments.findIndex(
        segment => segment.id === segmentId,
      );
      if (segmentIndex !== -1) {
        this.chatSegments[segmentIndex].images = updatedImages;
        const updatedSegment = {...this.chatSegments[segmentIndex]};
        this.$nextTick(() => {
          this.chatSegments.splice(segmentIndex, 1, updatedSegment);
        });
      }
    },

    async processAssistantMessage(message: any) {
      // messageParserService instance is now in useChatStreaming,
      // but this method is for initial load, not streaming.
      // So, a local or temporary instance might be needed if this logic is complex.
      // For simplicity, if messageArtifactService doesn't rely on a stateful parser for this, it's fine.
      // Otherwise, this might need to pass a new MessageParserService instance or adapt.
      const {segments} = messageArtifactService.processAssistantMessage(
        message,
        () => this.generateUniqueId(),
      );
      for (const segment of segments) {
        this.chatSegments.push(segment);
        if (segment.type === 'brief_artifact' && segment.rawContent)
          this.finalBrief = segment.rawContent;
        else if (segment.type === 'email_artifact') {
          // If we have a design property, use it directly
          if (segment.design) {
            this.generatedEmailDesign = segment.design;
          }
          // If we have email component JSON but no design yet, regenerate the design
          else if (segment.rawContent) {
            console.log(
              'Found email artifact without design, generating from components',
            );
            try {
              // Generate the email design from the components
              await this.generateEmailFromComponents(
                segment.rawContent,
                segment.id,
              );
            } catch (error) {
              console.error(
                'Error generating email design from loaded components:',
                error,
              );
            }
          }
        } else if (segment.type === 'plan_artifact' && segment.planData)
          this.generatedPlanData = segment.planData;
      }
    },

    processToolMessage(message: any) {
      // Extract tool name from metadata if available
      const toolName = message.llmMetadata?.name || 'tool';
      const randomMessage = ToolMessageService.getRandomMessage(toolName);

      this.chatSegments.push({
        id: this.generateUniqueId(),
        type: 'tool_message',
        sender: 'ai',
        content: randomMessage,
        timestamp: new Date(message.createdAt),
      });
    },

    toggleArtifactDrawer() {
      this.isArtifactDrawerOpen = !this.isArtifactDrawerOpen;
    },

    handleArtifactWidthChange(width: number) {
      this.artifactDrawerWidth = width;
    },

    handleRetryParse() {
      const planArtifact = this.chatSegments.find(
        (s: ChatMessageSegment) =>
          s.type === 'plan_artifact' && s.hasParseError === true,
      );
      if (planArtifact && planArtifact.rawContent) {
        this.requestPlanFormatFix(planArtifact.rawContent);
      } else {
        console.error('Could not find plan artifact with parse error to retry');
      }
    },

    handleCampaignBuiltFromArtifact({campaignId, taskId}) {
      console.log('Campaign built from artifact:', {campaignId, taskId});
      this.chatSegments.push({
        id: this.generateUniqueId(),
        type: 'text',
        sender: 'ai',
        content: `I've created a campaign for you! You can view and edit the tasks for this campaign [here](/ai-strategist/tasks/${taskId}).`,
        isHtml: true,
        timestamp: new Date(),
      });

      // Close the artifact drawer after successful campaign creation
      this.toggleArtifactDrawer();

      // Navigate to the task detail page
      this.$router.push(`/ai-strategist/tasks/${taskId}`);
    },

    generateUniqueId(): string {
      return `comp-seg-${Date.now()}-${Math.random()
        .toString(36)
        .substring(7)}`;
    },

    getArtifactType(type: string): 'brief' | 'email' | 'plan' {
      if (type.includes('email')) return 'email';
      if (type.includes('plan')) return 'plan';
      return 'brief';
    },

    scrollToBottom(forceScroll = false) {
      nextTick(() => {
        const chatHistory = this.$refs.chatHistoryContainer as HTMLElement;
        if (chatHistory) {
          if (forceScroll) {
            // Manual scroll - always scroll to bottom
            chatHistory.scrollTop = chatHistory.scrollHeight;
            this.isAtBottom = true;
          } else {
            // Auto-scroll during streaming - check if user is at bottom first
            const {scrollTop, scrollHeight, clientHeight} = chatHistory;
            const isNearBottom = scrollHeight - scrollTop - clientHeight < 20;

            if (isNearBottom) {
              chatHistory.scrollTop = chatHistory.scrollHeight;
              this.isAtBottom = true;
            }
          }
        } else {
          console.error('chatHistoryContainer ref not found');
        }
      });
    },

   sendMessage() {
      if (!this.canSendMessage) {
        this.showQuotaModal = true;
        return;
      }
      const text = this.newMessage.trim();
      if (
        (!text && this.attachedImages.length === 0) ||
        this.isAnyContentGenerating
      ) {
        // If AI is generating content, provide feedback but keep input focused
        if (this.isAnyContentGenerating) {
          // Focus stays on the input so user can keep typing
          this.$nextTick(() => {
            if (this.chatInput) this.chatInput.focus();
          });
        }

        // Re-focus the chat input if it exists
        if (this.chatInput) {
          this.$nextTick(() => {
            this.chatInput.focus();
          });
        }
        return;
      }

      const messageContent: Array<any> = [];
      if (text) messageContent.push({type: 'text', text: text});
      for (const image of this.attachedImages) {
        messageContent.push({
          type: 'image_url',
          image_url: {url: image.dataUrl},
        });
      }

      this.newMessage = '';
      this.attachedImages = [];
      this.isWaitingForAI = true;

      // Update quota immediately when sending message
      this.quotaService.addMessage(1);

      if (!this.conversationId) {
        this.handleStartConversation(messageContent);
      } else {
        this.handlePostMessage(this.conversationId, messageContent, false);
      }
      customerIOTrackEvent('Sent Plan Chat Message');
      this.scrollToBottom();
    },

    async handleStartConversation(initialMessage: string | MessageContent[]) {
      const payload: {
        message: string | MessageContent[];
        promptTemplateId?: number;
      } = {message: initialMessage};
      if (this.selectedAction === 'planCampaigns')
        payload.promptTemplateId = 10;
      else if (this.selectedAction === 'planMonth')
        payload.promptTemplateId = 11;
      else if (this.selectedAction === 'flows')
        payload.promptTemplateId = 13;
	  else if (this.selectedAction === 'brainstorm')
        payload.promptTemplateId = 14;

      if (typeof initialMessage === 'string') {
        this.chatSegments.push({
          id: this.generateUniqueId(),
          type: 'text',
          sender: 'user',
          content: initialMessage,
          timestamp: new Date(),
        });
      } else if (Array.isArray(initialMessage)) {
        for (const item of initialMessage) {
          if (item.type === 'text' && item.text) {
            this.chatSegments.push({
              id: this.generateUniqueId(),
              type: 'text',
              sender: 'user',
              content: item.text,
              timestamp: new Date(),
            });
          } else if (item.type === 'image_url' && item.image_url?.url) {
            this.chatSegments.push({
              id: this.generateUniqueId(),
              type: 'image',
              sender: 'user',
              imageUrl: item.image_url.url,
              imageLoaded: false,
              timestamp: new Date(),
            });
          }
        }
      }
      customerIOTrackEvent('New Plan Chat');
      const streamResult = await this.processStream(startConversation(payload, true, true, true, true, true));
      await this.quotaService.refreshQuota();

      // Update URL with conversation_id when a new conversation starts
      if (this.conversationId) {
        const url = new URL(window.location.href);
        url.searchParams.set('conversation_id', this.conversationId);
        window.history.replaceState({}, '', url.toString());
      }
    },

    async handlePostMessage(
      conversationIdValue: string,
      message: string | MessageContent[],
      isHidden: boolean = false,
    ) {
      if (!isHidden) {
        if (typeof message === 'string') {
          this.chatSegments.push({
            id: this.generateUniqueId(),
            type: 'text',
            sender: 'user',
            content: message,
            timestamp: new Date(),
          });
        } else if (Array.isArray(message)) {
          for (const item of message) {
            if (item.type === 'text' && item.text) {
              this.chatSegments.push({
                id: this.generateUniqueId(),
                type: 'text',
                sender: 'user',
                content: item.text,
                timestamp: new Date(),
              });
            } else if (item.type === 'image_url' && item.image_url?.url) {
              this.chatSegments.push({
                id: this.generateUniqueId(),
                type: 'image',
                sender: 'user',
                imageUrl: item.image_url.url,
                imageLoaded: false,
                timestamp: new Date(),
              });
            }
          }
        }
      }
      await this.processStream(postMessage(conversationIdValue, {message}, true, true, true, true, true));
      await this.quotaService.refreshQuota();
    },

    async generateEmailFromComponents(
      componentJSONText: string,
      placeholderIdToUpdate: string | null = null,
    ) {
      console.log(
        'Calling API to generate email with components:',
        componentJSONText,
      );
      try {
        // Parse the component JSON
        const componentJSON = JSON.parse(componentJSONText);
        console.log('Parsed component JSON:', componentJSON);

        // Make the API call to generate the email
        const response = await fetch(
          `${Utils.URL_DOMAIN}/planner/generate-email-from-component-list`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(componentJSON),
          },
        );
        const responseJSON = await response.json();
        console.log('Email generation API response received:', !!responseJSON);

        const targetPlaceholderId = placeholderIdToUpdate;

        // Update the UI based on the response
        if (targetPlaceholderId) {
          const placeholderIndex = this.chatSegments.findIndex(
            (s: ChatMessageSegment) => s.id === targetPlaceholderId,
          );
          if (placeholderIndex !== -1) {
            // Update existing placeholder with generated email
            console.log(
              `Updating email placeholder ${targetPlaceholderId} with generated design`,
            );
            this.chatSegments[placeholderIndex] = {
              ...this.chatSegments[placeholderIndex],
              type: 'email_artifact',
              isGenerating: false,
              design: responseJSON,
            };
            // Set the design for the artifact drawer
            this.generatedEmailDesign = responseJSON;
          } else {
            // Placeholder not found, create a new segment
            console.error(
              'Could not find email placeholder to update:',
              targetPlaceholderId,
            );
            this.chatSegments.push({
              id: this.generateUniqueId(),
              type: 'email_artifact',
              sender: 'ai',
              isGenerating: false,
              design: responseJSON,
              timestamp: new Date(),
            });
            this.generatedEmailDesign = responseJSON;
          }
        } else {
          // No placeholder ID provided, create a new segment
          console.error(
            'Email generated but no placeholder ID was provided for update.',
          );
          this.chatSegments.push({
            id: this.generateUniqueId(),
            type: 'email_artifact',
            sender: 'ai',
            isGenerating: false,
            design: responseJSON,
            timestamp: new Date(),
          });
          this.generatedEmailDesign = responseJSON;
        }

        // Ensure the loading state is reset
        this.isGeneratingEmail = false;

        // Update any listeners about the successful generation
        toolEvents.emit('email:complete', {
          design: responseJSON,
          placeholderId: targetPlaceholderId,
        });
        customerIOTrackEvent('Generated Email');
        return responseJSON;
      } catch (error: any) {
        // Handle errors
        console.error('Error calling email generation API:', error);

        // Remove the placeholder if it exists
        if (placeholderIdToUpdate) {
          const placeholderIndex = this.chatSegments.findIndex(
            (s: ChatMessageSegment) => s.id === placeholderIdToUpdate,
          );
          if (placeholderIndex !== -1)
            this.chatSegments.splice(placeholderIndex, 1);
        }

        // Add an error message
        this.chatSegments.push({
          id: this.generateUniqueId(),
          type: 'text',
          sender: 'ai',
          content: 'Error: Failed to generate email.',
          timestamp: new Date(),
        });

        // Ensure the loading state is reset
        this.isGeneratingEmail = false;

        // Update any listeners about the error
        toolEvents.emit('email:error', {
          error: error.message || 'Unknown error',
          placeholderId: placeholderIdToUpdate,
        });

        throw error; // Re-throw so the caller can handle it
      } finally {
        // Always scroll to the bottom and ensure generating state is reset
        this.scrollToBottom();
        this.isGeneratingEmail = false;
      }
    },

    handleArtifactClick(segment: ChatMessageSegment) {
      console.log('Artifact clicked:', segment.type, segment.id);
      if (
        segment.type === 'brief_artifact' ||
        segment.type === 'brief_placeholder'
      ) {
        this.finalBrief =
          segment.rawContent ||
          (segment.type === 'brief_placeholder' && this.isWaitingForAI
            ? this.streamingBriefContent
            : null);
        this.artifactInitialView = 'brief';
        this.isArtifactDrawerOpen = true;
      } else if (
        segment.type === 'email_artifact' ||
        segment.type === 'email_placeholder'
      ) {
        this.generatedEmailDesign = segment.design || null;
        this.artifactInitialView = 'email';
        this.isArtifactDrawerOpen = true;
      } else if (
        segment.type === 'plan_artifact' ||
        segment.type === 'plan_placeholder'
      ) {
        this.generatedPlanData = segment.planData || null;
        this.artifactInitialView = 'plan';
        this.isArtifactDrawerOpen = true;
      }
    },

    handleSwitchModeClick(switchModeData: { message: string; mode: string; summary: string }) {
      console.log('Switch mode clicked:', switchModeData);

      // Set the selectedAction based on the mode
      const modeMap: Record<string, 'planMonth' | 'planCampaigns' | 'brainstorm' | 'flows'> = {
        'planMonth': 'planMonth',
        'plan my month': 'planMonth',
        'planCampaigns': 'planCampaigns',
        'send an email': 'planCampaigns',
        'brainstorm': 'brainstorm',
        'flows': 'flows',
        'Analyze Flows': 'flows' // Current mapping
      };

      const newMode = modeMap[switchModeData.mode];
      if (newMode) {
        // Clear current conversation to start fresh
        this.chatSegments = [];
        this.conversationId = null;
        this.isWaitingForAI = false;

        // Set the new mode
        this.selectedAction = newMode;

        // Clear URL parameters to avoid any conflicts
        const cleanUrl = new URL(window.location.href);
        cleanUrl.searchParams.delete('conversation_id');
        cleanUrl.searchParams.delete('mode');
        cleanUrl.searchParams.delete('summary');
        window.history.replaceState({}, '', cleanUrl.toString());

        // If there's a summary, automatically start a new conversation with it
        if (switchModeData.summary && switchModeData.summary.trim()) {
          this.newMessage = switchModeData.summary;
          // Small delay to ensure UI updates, then start new conversation
          this.$nextTick(() => {
            this.sendMessage(); // This will call handleStartConversation since conversationId is null
          });
        }
      }
    },

    async requestPlanFormatFix(_planContent: string) {
      try {
        console.log('Automatically requesting plan format fix');
        const fixRequestMessage =
          "The plan data couldn't be processed correctly. Please regenerate the plan in valid JSON format. Make sure all properties are properly quoted and all JSON syntax is correct. Ensure the plan is wrapped in proper <plan></plan> tags with no text between the closing tag and the JSON content.";
        if (this.conversationId) {
          const payload = {message: fixRequestMessage};
          await this.processStream(postMessage(this.conversationId, payload, true, true, true, true, true));
        }
      } catch (error: any) {
        console.error('Error requesting plan format fix:', error);
        this.chatSegments.push({
          id: this.generateUniqueId(),
          type: 'text',
          sender: 'ai',
          content:
            "I'm having trouble creating your plan right now. Let me try a different approach. Could you provide a bit more detail about what you're looking for?",
          timestamp: new Date(),
        });
      }
    },

    handlePromptSelected(promptText: string) {
      this.newMessage = promptText;
      this.sendMessage();
    },

    handleEnterKey(event: KeyboardEvent) {
      event.preventDefault();
      if (!this.isAnyContentGenerating) {
        this.sendMessage();
      } else {
        // Ensure chat input stays focused
        if (this.chatInput) {
          this.$nextTick(() => {
            this.chatInput.focus();
          });
        }
      }
    },

    handleShiftEnterKey(event: KeyboardEvent) {
      this.$nextTick(() => {
        this.autoResize(event);
      });
    },

    async generateImage(params: any, placeholderId: string) {
      try {
        const response = await fetch(
          `${Utils.URL_DOMAIN}/chat/conversations/${this.conversationId}/generate-image`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              prompt: params.prompt,
              quality: params.quality || 'standard',
              style: params.style || 'vivid',
              size: params.size || '1024x1024',
              n: params.n || 1,
            }),
          },
        );
        const result = await response.json();
        if (!response.ok)
          throw new Error(result.message || 'Failed to generate image');
        const placeholderIndex = this.chatSegments.findIndex(
          s => s.id === placeholderId,
        );
        if (placeholderIndex !== -1) {
          if (result.generatedImages?.length) {
            this.chatSegments[placeholderIndex] = {
              ...this.chatSegments[placeholderIndex],
              isGenerating: false,
              imageUrl: result.generatedImages[0].url,
              imageLoaded: false,
              imageError: false,
            };
          } else {
            this.chatSegments.splice(placeholderIndex, 1);
            throw new Error('No image was generated');
          }
        }
      } catch (error: any) {
        console.error('Error generating image:', error);
        const placeholderIndex = this.chatSegments.findIndex(
          s => s.id === placeholderId,
        );
        if (placeholderIndex !== -1)
          this.chatSegments.splice(placeholderIndex, 1);
        this.chatSegments.push({
          id: this.generateUniqueId(),
          type: 'text',
          sender: 'ai',
          content: `Error generating image: ${
            error.message || 'Please try again'
          }`,
          timestamp: new Date(),
        });
      }
      customerIOTrackEvent('Generated Image');
    },

    async editImage(params: any, placeholderId: string) {
      try {
        const requestBody = {
          images: params.images,
          mask: params.mask,
          prompt: params.prompt,
          size: params.size,
          n: params.n,
        };
        const response = await fetch(
          `${Utils.URL_DOMAIN}/chat/conversations/${this.conversationId}/edit-image`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
          },
        );
        const result = await response.json();
        if (!response.ok)
          throw new Error(result.message || 'Failed to edit image');
        const placeholderIndex = this.chatSegments.findIndex(
          s => s.id === placeholderId,
        );
        if (placeholderIndex !== -1) {
          if (result.editedImages?.length) {
            this.chatSegments[placeholderIndex] = {
              ...this.chatSegments[placeholderIndex],
              isGenerating: false,
              imageUrl: result.editedImages[0].url,
              imageLoaded: false,
              imageError: false,
            };
          } else {
            this.chatSegments.splice(placeholderIndex, 1);
            throw new Error('No image was generated from edit');
          }
        }
      } catch (error: any) {
        console.error('Error editing image:', error);
        const placeholderIndex = this.chatSegments.findIndex(
          s => s.id === placeholderId,
        );
        if (placeholderIndex !== -1)
          this.chatSegments.splice(placeholderIndex, 1);
        this.chatSegments.push({
          id: this.generateUniqueId(),
          type: 'text',
          sender: 'ai',
          content: `Error editing image: ${
            error.message || 'Please try again'
          }`,
          timestamp: new Date(),
        });
      }
    },

    async handleImageUpload(event: Event) {
      // Image upload functionality is disabled
      const input = event.target as HTMLInputElement;
      if (!input.files?.length) return;
      const files = Array.from(input.files);
      const maxSize = 10 * 1024 * 1024;
      const allowedTypes = [
        'image/png',
        'image/jpeg',
        'image/jpg',
        'image/webp',
      ];
      for (const file of files) {
        if (!allowedTypes.includes(file.type)) {
          alert('Please upload only PNG, JPG, or WEBP images.');
          continue;
        }
        if (file.size > maxSize) {
          alert('Image size should be less than 10MB');
          continue;
        }
        try {
          const dataUrl = await this.fileToDataURL(file);
          this.attachedImages.push({file, dataUrl});
        } catch (error: any) {
          console.error('Error converting image to data URL:', error);
          alert('Failed to process image');
        }
      }
      input.value = '';
    },

    async handleImageUploadFromToken(
      base64DataUrl: string,
      style?: string,
    ): Promise<string | undefined> {
      try {
        const response = await fetch(base64DataUrl);
        const blob = await response.blob();
        const dimensions = await new Promise<{width: number; height: number}>(
          resolve => {
            const img = new Image();
            img.onload = () => resolve({width: img.width, height: img.height});
            img.src = base64DataUrl;
          },
        );
        const friendlyName = 'Generated Image';
        const defaultDescription =
          'AI Generated image for use in email communications';
        const queryParams = new URLSearchParams({
          name: friendlyName,
          imageType: style || '',
          assetType: 'email',
          description: defaultDescription,
          width: dimensions.width.toString(),
          height: dimensions.height.toString(),
          temp: 'true',
        });
        const formData = new FormData();
        formData.append('file', blob, 'generated-image.png');
        const token = localStorage.getItem('token');
        if (!token) throw new Error('No auth token found');
        const uploadResponse = await fetch(
          `${Utils.URL_DOMAIN}/branding/image/upload?${queryParams.toString()}`,
          {
            method: 'POST',
            headers: {Authorization: `Bearer ${token}`},
            body: formData,
          },
        );
        if (!uploadResponse.ok) throw new Error('Failed to upload image');
        const imageUrl = await uploadResponse.text();
        const metadataResponse = await fetch(
          `${Utils.URL_DOMAIN}/branding/images/metadata`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              url: imageUrl,
              width: dimensions.width,
              height: dimensions.height,
              friendlyname: friendlyName,
              assetType: 'email',
              imageType: style || '',
              description: defaultDescription,
            }),
          },
        );
        if (!metadataResponse.ok)
          throw new Error('Failed to upload image metadata');
        return imageUrl;
      } catch (error: any) {
        console.error('Error uploading image:', error);
        throw error;
      }
    },

    removeImage(index: number) {
      this.attachedImages.splice(index, 1);
    },

    fileToDataURL(file: File): Promise<string> {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    },

    handleOnboardingComplete() {
      this.showOnboardingModal = false;
      this.cleanupOnboardingParams();
    },

    handleOnboardingSkip() {
      this.showOnboardingModal = false;
      this.cleanupOnboardingParams();
    },

    cleanupOnboardingParams() {
      // Remove onboarding parameter from URL
      const url = new URL(window.location.href);
      url.searchParams.delete('onboarding');
      window.history.replaceState({}, '', url.toString());
    },

    checkAndShowOnboardingModal() {
      // Check URL parameter first
      const urlParams = new URLSearchParams(window.location.search);
      const onboardingParam = urlParams.get('onboarding');

      // Show modal if onboarding=true in URL, or fallback to localStorage flag
      const needsOnboarding = localStorage.getItem('needs_onboarding_completion');
      if (onboardingParam === 'true' || needsOnboarding === 'true') {
        this.showOnboardingModal = true;
      }
    },
  },
  mounted() {
    initializeToolHandlers();

    // Check if user needs to complete onboarding
    this.checkAndShowOnboardingModal();

    if (typeof window !== 'undefined') {
      if (this.isMobile && !localStorage.getItem('chat_mobile_warning_shown')) {
        this.showMobileWarningModal = true;
      }
    }

    // Add resize listener
    if (typeof window !== 'undefined') {
      // Call handleResize initially to set the correct isMobile state
      // This was already called in setup's onMounted, but if onMounted
      // in setup runs after component's mounted, this ensures it.
      // However, the setup function's onMounted should run first.
      // Let's rely on the onMounted in setup.
    }

    // Create a MutationObserver to detect when the chat container content changes
    // This helps update the scroll position indicator during streaming
    nextTick(() => {
      const chatContainer = this.$refs.chatHistoryContainer as HTMLElement;
      if (chatContainer) {
        const observer = new MutationObserver(() => {
          this.handleScroll();

          // Only auto-scroll if we're already at the bottom
          if (this.isAtBottom) {
            this.scrollToBottom(false);
          }
        });

        observer.observe(chatContainer, {
          childList: true,
          subtree: true,
          characterData: true,
        });

        // Store the observer to disconnect it later
        this.chatObserver = observer;
      }
    });

    toolEvents.on('chat:update-segment', (payload: any) => {
      console.log('Received chat:update-segment event:', payload);
      if (payload && payload.type === 'text' && payload.content) {
        this.chatSegments.push({
          id: this.generateUniqueId(),
          type: 'text',
          sender: payload.sender || 'ai',
          content: payload.content,
          timestamp: payload.timestamp || new Date(),
        });
        this.scrollToBottom();
        return;
      }
      if (payload && payload.type === 'multiimage' && payload.images) {
        const existingIndex = this.chatSegments.findIndex(
          (segment: ChatMessageSegment) => segment.id === payload.placeholderId,
        );
        if (existingIndex !== -1) {
          this.chatSegments[existingIndex] = {
            ...this.chatSegments[existingIndex],
            type: 'multiimage',
            sender: 'ai',
            images: payload.images.map((img: any) => ({
              ...img,
              loaded: false,
              error: false,
            })),
            timestamp: new Date(),
          };
        } else {
          const segmentId = payload.placeholderId || this.generateUniqueId();
          this.chatSegments.push({
            id: segmentId,
            type: 'multiimage',
            sender: 'ai',
            images: payload.images.map((img: any) => ({
              ...img,
              loaded: false,
              error: false,
            })),
            timestamp: new Date(),
          });
        }
        this.scrollToBottom();
      } else if (payload && payload.type === 'memory' && payload.memory) {
        const existingIndex = this.chatSegments.findIndex(
          (segment: ChatMessageSegment) => segment.id === payload.placeholderId,
        );
        if (existingIndex !== -1) {
          this.chatSegments[existingIndex] = {
            ...this.chatSegments[existingIndex],
            type: 'memory',
            sender: 'ai',
            memory: payload.memory,
            timestamp: new Date(),
          } as any;
        } else {
          const segmentId = payload.placeholderId || this.generateUniqueId();
          this.chatSegments.push({
            id: segmentId,
            type: 'memory',
            sender: 'ai',
            memory: payload.memory,
            timestamp: new Date(),
          } as any);
        }
        this.scrollToBottom();
      }
    });
    const urlParams = new URLSearchParams(window.location.search);
    const conversationIdFromUrl = urlParams.get('conversation_id');
    if (conversationIdFromUrl) {
      this.loadConversation(conversationIdFromUrl);
    }

    // Handle mode and summary parameters for switch mode functionality
    const modeParam = urlParams.get('mode');
    const summaryParam = urlParams.get('summary');

    if (modeParam) {
      // Map the mode parameter to selectedAction
      const modeMap: Record<string, 'planMonth' | 'planCampaigns' | 'brainstorm' | 'flows'> = {
        'planMonth': 'planMonth',
        'plan my month': 'planMonth',
        'planCampaigns': 'planCampaigns',
        'send an email': 'planCampaigns',
        'brainstorm': 'brainstorm',
        'flows': 'flows',
        'Analyze Flows': 'flows' // Current mapping
      };

      if (modeMap[modeParam]) {
        this.selectedAction = modeMap[modeParam];
        console.log('Set selectedAction from URL param:', this.selectedAction);
      }

      // If there's a summary, set it as the message and auto-send
      if (summaryParam && summaryParam.trim()) {
        this.newMessage = summaryParam;
        console.log('Set newMessage from URL param:', summaryParam);

        // Auto-send the message after a short delay to ensure UI is ready
        this.$nextTick(() => {
          setTimeout(() => {
            this.sendMessage();
          }, 500);
        });
      }

      // Clean up URL parameters after processing them
      const cleanUrl = new URL(window.location.href);
      cleanUrl.searchParams.delete('mode');
      cleanUrl.searchParams.delete('summary');
      window.history.replaceState({}, '', cleanUrl.toString());
    }
  },
  beforeUnmount() {
    toolEvents.off('chat:update-segment');

    // Disconnect the MutationObserver when component is unmounted
    if (this.chatObserver) {
      this.chatObserver.disconnect();
    }
    // Remove resize listener - already handled in setup's onBeforeUnmount
  },
});
</script>

<style scoped>
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #b1b2b4 #f3f4f6;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 9999px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: #8e9299;
  border-radius: 9999px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #8e9299;
}
</style>
