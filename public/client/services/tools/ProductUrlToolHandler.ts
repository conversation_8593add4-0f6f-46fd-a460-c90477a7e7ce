import { BaseToolHandler } from '../toolHandlerService';
import { Emitter } from 'mitt';
import { getOrganizationSetting, updateOrganizationSetting } from '../organization-settings.js';

interface ProductUrlData {
  name: string;
  productUrl?: string;
  imageUrl?: string;
  id?: string;
  price?: number;
  salesData?: {
    quantitySold?: number;
    totalRevenue?: number;
    rank?: number;
  };
}

interface StoredProductUrl {
  type: 'product_url';
  data: ProductUrlData;
  timestamp: string;
  source: string; // 'product_lookup', 'best_sellers', etc.
}

export class ProductUrlToolHandler extends BaseToolHandler {
  public readonly tag = 'product_url_capture';

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register();
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    this.streamingContent += contentChunk;
  }

  private async saveProductUrlsToStorage(productUrls: ProductUrlData[], source: string): Promise<boolean> {
    try {
      // Get existing product URLs
      const existingUrlsString = await getOrganizationSetting('productUrls');
      let existingUrls: StoredProductUrl[] = [];

      if (existingUrlsString) {
        try {
          existingUrls = JSON.parse(existingUrlsString);
        } catch (parseError) {
          console.warn('Failed to parse existing product URLs, starting fresh:', parseError);
          existingUrls = [];
        }
      }

      // Convert new product URLs to stored format
      const newStoredUrls: StoredProductUrl[] = productUrls.map(productUrl => ({
        type: 'product_url',
        data: productUrl,
        timestamp: new Date().toISOString(),
        source: source
      }));

      // Check for duplicates based on product ID or URL
      const filteredNewUrls = newStoredUrls.filter(newUrl => {
        const isDuplicate = existingUrls.some(existing => {
          if (newUrl.data.id && existing.data.id) {
            return existing.data.id === newUrl.data.id;
          }
          if (newUrl.data.productUrl && existing.data.productUrl) {
            return existing.data.productUrl === newUrl.data.productUrl;
          }
          return existing.data.name.toLowerCase() === newUrl.data.name.toLowerCase();
        });
        return !isDuplicate;
      });

      if (filteredNewUrls.length === 0) {
        console.log('ProductUrlToolHandler: No new product URLs to save (all duplicates)');
        return false;
      }

      // Add new URLs to existing ones (most recent first)
      existingUrls.unshift(...filteredNewUrls);

      // Limit to last 100 product URLs to prevent bloat
      const MAX_PRODUCT_URLS = 100;
      if (existingUrls.length > MAX_PRODUCT_URLS) {
        existingUrls = existingUrls.slice(0, MAX_PRODUCT_URLS);
      }

      // Save back to organization settings
      await updateOrganizationSetting('productUrls', JSON.stringify(existingUrls));
      console.log(`ProductUrlToolHandler: Successfully saved ${filteredNewUrls.length} product URLs to storage`);
      return true;
    } catch (error) {
      console.error('ProductUrlToolHandler: Failed to save product URLs to storage:', error);
      return false;
    }
  }

  async captureProductUrls(toolResult: any, source: string): Promise<void> {
    try {
      if (!toolResult || !toolResult.productUrls || !Array.isArray(toolResult.productUrls)) {
        return;
      }

      const productUrls = toolResult.productUrls as ProductUrlData[];
      if (productUrls.length === 0) {
        return;
      }

      console.log(`ProductUrlToolHandler: Capturing ${productUrls.length} product URLs from ${source}`);
      
      const saved = await this.saveProductUrlsToStorage(productUrls, source);
      
      if (saved) {
        // Emit success message for UI display
        this.emitter.emit('chat:update-segment', {
          type: 'product_url_capture',
          sender: 'system',
          message: `Captured ${productUrls.length} product URLs for future use`,
          timestamp: new Date(),
          placeholderId: this.placeholderId || `product-url-${Date.now()}`,
        });
      }
    } catch (error) {
      console.error('ProductUrlToolHandler: Error capturing product URLs:', error);
    }
  }

  onEnd(): void {
    // This handler doesn't use the standard streaming pattern
    // It's called directly when tool results contain product URLs
    this.placeholderId = null;
  }
}
