import { <PERSON><PERSON>ool<PERSON>and<PERSON> } from '../toolHandlerService';
import { Emitter } from 'mitt';
import { getOrganizationSetting, updateOrganizationSetting } from '../organization-settings.js';

interface MemoryPayload {
  category: string;
  info: string;
}

interface StoredMemory {
  type: string;
  content: string;
  timestamp: string;
}

export class MemoryToolHandler extends BaseToolHandler {
  public readonly tag = 'memory';

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register();
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    this.streamingContent += contentChunk;
  }

  private async saveMemoryToStorage(memory: MemoryPayload): Promise<boolean> {
    try {
      // Get existing memories
      const existingMemoriesString = await getOrganizationSetting('brandMemories');
      let existingMemories: StoredMemory[] = [];

      if (existingMemoriesString) {
        try {
          existingMemories = JSON.parse(existingMemoriesString);
        } catch (parseError) {
          console.warn('Failed to parse existing memories, starting fresh:', parseError);
          existingMemories = [];
        }
      }

      // Normalize content for duplicate detection (trim whitespace, ignore case)
      const normalizedNewContent = memory.info.trim().toLowerCase();

      // Check for duplicates - exact match on normalized content
      const isDuplicate = existingMemories.some(existing =>
        existing.content.trim().toLowerCase() === normalizedNewContent
      );

      if (isDuplicate) {
        console.log('MemoryToolHandler: Skipping duplicate memory:', memory.info);
        return false; // Indicate duplicate was skipped
      }

      // Map category to our new simplified types
      const typeMapping: Record<string, string> = {
        'info': 'info',
        'information': 'info',
        'brand info': 'info',
        'general': 'info',
        'about': 'info',
        'company': 'info',
        'text': 'text',
        'content': 'text',
        'messaging': 'text',
        'brand voice': 'preferences',
        'brand': 'preferences',
        'voice': 'preferences',
        'preference': 'preferences',
        'preferences': 'preferences',
        'style': 'preferences',
        'promotion': 'promotion',
        'promotional': 'promotion',
        'campaign': 'promotion',
        'offer': 'promotion',
        'discount': 'promotion',
        'email': 'email',
        'email template': 'email',
        'email design': 'email',
        'subject line': 'email',
        'brief': 'brief',
        'campaign brief': 'brief',
        'content brief': 'brief',
        'strategy': 'brief',
        'product': 'product',
        'product url': 'product',
        'product link': 'product',
        'product image': 'product',
        'image url': 'product',
        'button url': 'product'
      };

      // Determine memory type from category
      const memoryType = typeMapping[memory.category.toLowerCase()] || 'info';

      // Create new memory object
      const newMemory: StoredMemory = {
        type: memoryType,
        content: memory.info.trim(),
        timestamp: new Date().toISOString()
      };

      // Add to existing memories (most recent first)
      existingMemories.unshift(newMemory);

      // Limit to last 50 memories to prevent bloat
      const MAX_MEMORIES = 50;
      if (existingMemories.length > MAX_MEMORIES) {
        existingMemories = existingMemories.slice(0, MAX_MEMORIES);
      }

      // Save back to organization settings
      await updateOrganizationSetting('brandMemories', JSON.stringify(existingMemories));
      console.log('MemoryToolHandler: Successfully saved memory to storage:', newMemory);
      return true; // Indicate successful save
    } catch (error) {
      console.error('MemoryToolHandler: Failed to save memory to storage:', error);
      return false; // Indicate save failed
    }
  }

  private async captureProductUrlsFromMemory(memory: MemoryPayload): Promise<void> {
    try {
      // Check if the memory content contains product URLs
      const urlRegex = /https?:\/\/[^\s"'<>]+/g;
      const urls = memory.info.match(urlRegex);

      if (!urls || urls.length === 0) {
        return;
      }

      // Filter for product-related URLs
      const productUrls = urls.filter(url => {
        const lowerUrl = url.toLowerCase();
        return lowerUrl.includes('/products/') ||
               lowerUrl.includes('/product/') ||
               lowerUrl.includes('shopify.com') ||
               lowerUrl.includes('myshopify.com') ||
               memory.info.toLowerCase().includes('product') ||
               memory.info.toLowerCase().includes('image');
      });

      if (productUrls.length === 0) {
        return;
      }

      // Get existing product URLs
      const existingUrlsString = await getOrganizationSetting('productUrls');
      let existingUrls: any[] = [];

      if (existingUrlsString) {
        try {
          existingUrls = JSON.parse(existingUrlsString);
        } catch (parseError) {
          existingUrls = [];
        }
      }

      // Create product URL entries from memory
      const newProductUrls = productUrls.map(url => ({
        name: memory.info.substring(0, 50) + '...', // Use first part of memory as name
        productUrl: url.includes('/products/') ? url : null,
        imageUrl: url.includes('image') || url.includes('.jpg') || url.includes('.png') || url.includes('.webp') ? url : null,
        source: 'memory',
        timestamp: new Date().toISOString(),
        memoryContent: memory.info
      }));

      // Filter out duplicates
      const filteredUrls = newProductUrls.filter(newUrl => {
        return !existingUrls.some(existing =>
          existing.productUrl === newUrl.productUrl ||
          existing.imageUrl === newUrl.imageUrl
        );
      });

      if (filteredUrls.length === 0) {
        return;
      }

      // Add new URLs to existing ones
      existingUrls.unshift(...filteredUrls);

      // Limit to last 100 URLs
      if (existingUrls.length > 100) {
        existingUrls = existingUrls.slice(0, 100);
      }

      // Save back to organization settings
      await updateOrganizationSetting('productUrls', JSON.stringify(existingUrls));
      console.log(`MemoryToolHandler: Captured ${filteredUrls.length} product URLs from memory`);
    } catch (error) {
      console.error('MemoryToolHandler: Error capturing product URLs from memory:', error);
    }
  }

  private fixInvalidJson(jsonString: string): string {
    try {
      // First try to parse as-is
      JSON.parse(jsonString);
      return jsonString;
    } catch (e) {
      console.log('MemoryToolHandler: Attempting to fix invalid JSON format');

      // Fix common JSON issues:
      // 1. Unquoted property names
      let fixed = jsonString
        .replace(/(\w+):/g, '"$1":')  // Add quotes around property names
        .replace(/,\s*}/g, '}')       // Remove trailing commas
        .replace(/,\s*]/g, ']');      // Remove trailing commas in arrays

      try {
        JSON.parse(fixed);
        console.log('MemoryToolHandler: Successfully fixed JSON format');
        return fixed;
      } catch (e2) {
        console.log('MemoryToolHandler: Could not fix JSON, attempting manual parsing');

        // Manual parsing as last resort
        const categoryMatch = jsonString.match(/category:\s*["']([^"']+)["']/i);
        const infoMatch = jsonString.match(/info:\s*["']([^"']+)["']/i);

        if (categoryMatch && infoMatch) {
          const manualJson = {
            category: categoryMatch[1],
            info: infoMatch[1]
          };
          console.log('MemoryToolHandler: Manual parsing successful:', manualJson);
          return JSON.stringify(manualJson);
        }

        // If all else fails, return original
        throw new Error('Could not parse or fix JSON');
      }
    }
  }

  async onEnd(): Promise<void> {
    if (!this.placeholderId) return;
    let rawContent = this.streamingContent.trim();

    // Strip <memory> tags if they exist in the content
    rawContent = rawContent.replace(/^<memory>\s*/, '').replace(/\s*<\/memory>$/, '');

    try {
      // Try to fix and parse the JSON
	  console.log('MemoryToolHandler received raw content:', rawContent);
      const fixedJson = this.fixInvalidJson(rawContent);
      const memory: MemoryPayload = JSON.parse(fixedJson);
      console.log('MemoryToolHandler received memory:', memory);

      // Validate that we have the required fields
      if (!memory.category || !memory.info) {
        throw new Error('Missing required fields: category and info');
      }

      // Try to save to storage
      const saved = await this.saveMemoryToStorage(memory);

      // Also try to capture any product URLs from the memory
      await this.captureProductUrlsFromMemory(memory);

      if (saved) {
        // Emit success message for UI display
        this.emitter.emit('chat:update-segment', {
          type: 'memory',
          sender: 'ai',
          memory,
          timestamp: new Date(),
          placeholderId: this.placeholderId,
        });
      } else {
        // Memory was a duplicate or failed to save
        this.emitter.emit('chat:update-segment', {
          type: 'text',
          sender: 'ai',
          content: 'Memory already exists in your brand knowledge base.',
          timestamp: new Date(),
          placeholderId: this.placeholderId,
        });
      }
    } catch (e) {
      console.error('MemoryToolHandler failed to parse content:', e);
      console.error('Raw content was:', rawContent);
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `[Error parsing memory: ${e.message}]`
      });
    }
    this.emitter.emit(`${this.tag}:complete`, { placeholderId: this.placeholderId });
    this.placeholderId = null;
    this.streamingContent = '';
  }
}
