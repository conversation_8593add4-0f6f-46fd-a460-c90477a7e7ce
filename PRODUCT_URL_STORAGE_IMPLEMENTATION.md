# Product URL Storage Implementation

## Overview
This implementation replaces the previous storage of product URLs in the `llmMetadata` field with a dedicated database table and service architecture for better data management and retrieval.

## Changes Made

### 1. Database Schema
- **New Table**: `conversation_product_url`
  - Stores product URLs captured from chat tool results
  - Links to conversations via foreign key
  - Includes product metadata (name, URLs, price, sales data)
  - Indexed for performance

### 2. New Models and Repositories
- **Model**: `ConversationProductUrl` (`src/models/conversation-product-url.model.ts`)
  - Defines the structure for product URL storage
  - Includes relations to Conversation model
  
- **Repository**: `ConversationProductUrlRepository` (`src/repositories/conversation-product-url.repository.ts`)
  - Handles database operations for product URLs
  - Includes proper relations and constraints

### 3. New Service Layer
- **Service**: `ProductUrlService` (`src/services/product-url.service.ts`)
  - Centralized logic for retrieving product URLs
  - Handles fallback logic for finding conversations
  - Formats product URLs for prompt context
  - Replaces direct database queries in prompt context service

### 4. Updated Chat Service
- **File**: `src/services/chat/chat.service.ts`
  - Added `storeProductUrls()` method for saving captured product URLs
  - Removed product URL storage from `llmMetadata`
  - Integrated with new ConversationProductUrlRepository
  - Handles deduplication of product URLs

### 5. Updated Prompt Context Service
- **File**: `src/services/prompt/prompt-context.service.ts`
  - Simplified `getProductUrls()` method to use ProductUrlService
  - Removed complex conversation lookup logic
  - Removed dependency on ConversationRepository
  - Cleaner separation of concerns

### 6. Updated Chat Controller
- **File**: `src/controllers/chat.controller.ts`
  - Updated `product_lookup` and `best_sellers` tools to use organization external domain
  - Added `getOrganizationDomain()` method for proper URL generation
  - Improved product URL generation with real store domains

## Benefits

### 1. Better Data Structure
- Dedicated table with proper indexing
- Structured storage instead of JSON metadata
- Better query performance
- Easier data maintenance and migration

### 2. Improved Architecture
- Separation of concerns with dedicated service
- Centralized product URL logic
- Easier testing and maintenance
- Better error handling

### 3. Enhanced Functionality
- Proper deduplication logic
- Better product URL formatting
- Support for sales data and pricing
- Improved conversation linking

### 4. Production Ready
- Removed all debugging code
- Cleaned up unused imports and variables
- Proper error handling
- Database migration script included

## Migration

### Database Migration
Run the migration script: `migrations/create_conversation_product_url_table.sql`

### Data Migration (if needed)
If there are existing product URLs in `llmMetadata`, a data migration script would need to be created to move them to the new table structure.

## Usage

### Storing Product URLs
Product URLs are automatically stored when chat tools (`product_lookup`, `best_sellers`) return results with product information.

### Retrieving Product URLs
The `ProductUrlService.getProductUrls(task, campaign)` method handles all retrieval logic with proper fallback mechanisms.

### Prompt Integration
Product URLs are automatically included in prompts via the `{PRODUCT_URLS}` tag, which now uses the new storage mechanism.

## Files Modified

### New Files
- `src/models/conversation-product-url.model.ts`
- `src/repositories/conversation-product-url.repository.ts`
- `src/services/product-url.service.ts`
- `migrations/create_conversation_product_url_table.sql`

### Modified Files
- `src/services/chat/chat.service.ts`
- `src/services/prompt/prompt-context.service.ts`
- `src/controllers/chat.controller.ts`
- `src/models/index.ts`
- `src/repositories/index.ts`
- `src/services/index.ts`

### Cleaned Up
- Removed debugging console.log statements
- Removed unused imports and variables
- Removed product URL storage from llmMetadata
- Simplified prompt context service logic

## Testing

To test the implementation:
1. Run `npm run start:dev`
2. Use chat tools that capture product URLs (`product_lookup`, `best_sellers`)
3. Verify product URLs are stored in the new table
4. Check that product URLs appear correctly in email generation prompts
5. Verify that product URLs use the organization's external domain

## Future Enhancements

1. **Data Migration**: Create script to migrate existing product URLs from llmMetadata
2. **Analytics**: Add analytics on product URL usage and effectiveness
3. **Caching**: Implement caching for frequently accessed product URLs
4. **Cleanup**: Add cleanup job for old product URLs
5. **API**: Expose API endpoints for managing product URLs directly
